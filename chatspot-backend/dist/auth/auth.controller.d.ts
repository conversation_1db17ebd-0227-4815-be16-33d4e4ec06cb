import { AuthService } from './auth.service';
import { AuthCredentialsDto, AuthResponseDto, RefreshTokenDto, UserDto } from './dto';
export declare class AuthController {
    private readonly authService;
    constructor(authService: AuthService);
    register(authCredentialsDto: AuthCredentialsDto): Promise<AuthResponseDto>;
    login(authCredentialsDto: AuthCredentialsDto): Promise<AuthResponseDto>;
    getCurrentUser(req: any): Promise<UserDto>;
    refreshToken(refreshTokenDto: RefreshTokenDto): Promise<AuthResponseDto>;
    logout(refreshTokenDto: RefreshTokenDto): Promise<{
        message: string;
    }>;
    logoutAll(req: any): Promise<{
        message: string;
    }>;
}
