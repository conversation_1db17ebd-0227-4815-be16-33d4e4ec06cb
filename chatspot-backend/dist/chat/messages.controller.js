"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.MessagesController = void 0;
const common_1 = require("@nestjs/common");
const message_service_1 = require("./message.service");
const jwt_auth_guard_1 = require("../auth/jwt-auth.guard");
const swagger_1 = require("@nestjs/swagger");
const message_dto_1 = require("./dto/message.dto");
const chat_gateway_1 = require("./chat.gateway");
let MessagesController = class MessagesController {
    messageService;
    chatGateway;
    constructor(messageService, chatGateway) {
        this.messageService = messageService;
        this.chatGateway = chatGateway;
    }
    async getAllMessages(req) {
        const username = req.user.username;
        const messages = await this.messageService.getAllMessagesForUsername(username);
        const pendingMessagesForUser = messages.filter(msg => msg.receiver_username === username && msg.status === 'pending');
        const deliveredMessagesSentByUser = messages.filter(msg => msg.sender_username === username && msg.status === 'delivered');
        if (deliveredMessagesSentByUser.length > 0) {
            console.log(`Found ${deliveredMessagesSentByUser.length} delivered messages sent by ${username}`);
            try {
                const messageIds = deliveredMessagesSentByUser.map(msg => msg.id);
                console.log(`Deleting ${messageIds.length} delivered messages for sender ${username}: ${messageIds.join(', ')}`);
                for (const messageId of messageIds) {
                    await this.messageService.delete(messageId);
                }
                console.log(`Successfully deleted ${messageIds.length} delivered messages for sender ${username}`);
                const deletedMessageIds = new Set(messageIds);
                const filteredMessages = messages.filter(msg => !deletedMessageIds.has(msg.id));
                this.markPendingMessagesAsDeliveredAsync(pendingMessagesForUser, username);
                return filteredMessages;
            }
            catch (error) {
                console.error(`Failed to delete delivered messages for sender ${username}:`, error);
            }
        }
        this.markPendingMessagesAsDeliveredAsync(pendingMessagesForUser, username);
        return messages;
    }
    async getConversationMessages(req, otherUsername) {
        const username = req.user.username;
        const messages = await this.messageService.getMessagesForConversation(username, otherUsername);
        const pendingMessagesForUser = messages.filter(msg => msg.receiver_username === username &&
            msg.sender_username === otherUsername &&
            msg.status === 'pending');
        if (pendingMessagesForUser.length > 0) {
            this.markPendingMessagesAsDeliveredAsync(pendingMessagesForUser, username);
        }
        return messages;
    }
    async getPendingMessages(req) {
        const username = req.user.username;
        const messages = await this.messageService.getPendingMessagesForUsername(username);
        const pendingMessagesForUser = messages.filter(msg => msg.receiver_username === username && msg.status === 'pending');
        if (pendingMessagesForUser.length > 0) {
            this.markPendingMessagesAsDeliveredAsync(pendingMessagesForUser, username);
        }
        return messages;
    }
    markPendingMessagesAsDeliveredAsync(pendingMessages, username) {
        setImmediate(async () => {
            for (const message of pendingMessages) {
                try {
                    await this.messageService.markAsDelivered(message.id);
                    const deliveryNotified = this.chatGateway.notifyMessageDelivered(message.sender_username, message.id, message.receiver_username, message.client_message_id);
                    console.log(`Message ${message.id} marked as delivered for ${username}. Sender notified: ${deliveryNotified}`);
                }
                catch (error) {
                    console.error(`Failed to mark message ${message.id} as delivered:`, error);
                }
            }
        });
    }
};
exports.MessagesController = MessagesController;
__decorate([
    (0, common_1.Get)(),
    (0, swagger_1.ApiOperation)({ summary: 'Get all messages for the authenticated user' }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Returns all messages for the user',
        type: [message_dto_1.MessageDto],
    }),
    (0, swagger_1.ApiResponse)({ status: 401, description: 'Unauthorized' }),
    __param(0, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], MessagesController.prototype, "getAllMessages", null);
__decorate([
    (0, common_1.Get)('conversation/:otherUsername'),
    (0, swagger_1.ApiOperation)({ summary: 'Get messages for a specific conversation' }),
    (0, swagger_1.ApiParam)({
        name: 'otherUsername',
        description: 'Username of the other participant in the conversation',
        example: 'johndoe'
    }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Returns messages for the conversation',
        type: [message_dto_1.MessageDto],
    }),
    (0, swagger_1.ApiResponse)({ status: 401, description: 'Unauthorized' }),
    __param(0, (0, common_1.Request)()),
    __param(1, (0, common_1.Param)('otherUsername')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, String]),
    __metadata("design:returntype", Promise)
], MessagesController.prototype, "getConversationMessages", null);
__decorate([
    (0, common_1.Get)('pending'),
    (0, swagger_1.ApiOperation)({ summary: 'Get pending messages for the authenticated user' }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Returns pending messages for the user',
        type: [message_dto_1.MessageDto],
    }),
    (0, swagger_1.ApiResponse)({ status: 401, description: 'Unauthorized' }),
    __param(0, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], MessagesController.prototype, "getPendingMessages", null);
exports.MessagesController = MessagesController = __decorate([
    (0, swagger_1.ApiTags)('messages'),
    (0, common_1.Controller)('api/messages'),
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard),
    (0, swagger_1.ApiBearerAuth)('JWT-auth'),
    __param(1, (0, common_1.Inject)((0, common_1.forwardRef)(() => chat_gateway_1.ChatGateway))),
    __metadata("design:paramtypes", [message_service_1.MessageService,
        chat_gateway_1.ChatGateway])
], MessagesController);
//# sourceMappingURL=messages.controller.js.map