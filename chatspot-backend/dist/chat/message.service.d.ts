import { Repository } from 'typeorm';
import { Message } from './message.entity';
export declare class MessageService {
    private messageRepo;
    constructor(messageRepo: Repository<Message>);
    savePendingMessage(data: Partial<Message>): Promise<Message>;
    markAsDelivered(id: string): Promise<import("typeorm").UpdateResult>;
    delete(id: string): Promise<import("typeorm").DeleteResult>;
    getPendingMessagesForUser(userId: string): Promise<never[]>;
    getPendingMessagesForUsername(username: string): Promise<Message[]>;
    getAllMessagesForUsername(username: string): Promise<Message[]>;
    getMessagesForConversation(username1: string, username2: string): Promise<Message[]>;
    deleteDeliveredMessages(username: string): Promise<import("typeorm").DeleteResult>;
}
