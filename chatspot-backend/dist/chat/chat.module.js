"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ChatModule = void 0;
const common_1 = require("@nestjs/common");
const chat_gateway_1 = require("./chat.gateway");
const chat_service_1 = require("./chat.service");
const typeorm_1 = require("@nestjs/typeorm");
const message_entity_1 = require("./message.entity");
const message_service_1 = require("./message.service");
const messages_controller_1 = require("./messages.controller");
const auth_module_1 = require("../auth/auth.module");
const notifications_module_1 = require("../notifications/notifications.module");
const jwt_1 = require("@nestjs/jwt");
const users_service_1 = require("../auth/users.service");
const user_entity_1 = require("../auth/user.entity");
const config_1 = require("@nestjs/config");
let ChatModule = class ChatModule {
};
exports.ChatModule = ChatModule;
exports.ChatModule = ChatModule = __decorate([
    (0, common_1.Module)({
        imports: [
            typeorm_1.TypeOrmModule.forFeature([message_entity_1.Message, user_entity_1.User]),
            auth_module_1.AuthModule,
            notifications_module_1.NotificationsModule,
            jwt_1.JwtModule.registerAsync({
                imports: [config_1.ConfigModule],
                inject: [config_1.ConfigService],
                useFactory: (configService) => ({
                    secret: configService.get('JWT_SECRET', 'your_jwt_secret'),
                    signOptions: { expiresIn: configService.get('JWT_ACCESS_EXPIRATION', '15m') },
                }),
            }),
        ],
        controllers: [messages_controller_1.MessagesController],
        providers: [chat_gateway_1.ChatGateway, chat_service_1.ChatService, message_service_1.MessageService, users_service_1.UsersService],
    })
], ChatModule);
//# sourceMappingURL=chat.module.js.map