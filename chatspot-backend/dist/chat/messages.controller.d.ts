import { MessageService } from './message.service';
import { MessageDto } from './dto/message.dto';
import { ChatGateway } from './chat.gateway';
export declare class MessagesController {
    private readonly messageService;
    private readonly chatGateway;
    constructor(messageService: MessageService, chatGateway: ChatGateway);
    getAllMessages(req: any): Promise<MessageDto[]>;
    getConversationMessages(req: any, otherUsername: string): Promise<MessageDto[]>;
    getPendingMessages(req: any): Promise<MessageDto[]>;
}
