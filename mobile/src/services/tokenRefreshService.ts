import axios, { AxiosInstance } from 'axios';
import { authStorage } from '../utils/storage';
import { getApiUrl, debugLog } from '../utils/env';

interface RefreshTokenResponse {
  access_token: string;
  refresh_token: string;
}

class TokenRefreshService {
  private isRefreshing = false;
  private failedQueue: Array<{
    resolve: (value: string) => void;
    reject: (error: any) => void;
  }> = [];

  /**
   * Refresh the access token using the stored refresh token
   */
  async refreshToken(): Promise<string | null> {
    try {
      const refreshToken = await authStorage.getRefreshToken();
      if (!refreshToken) {
        debugLog('No refresh token available');
        return null;
      }

      const response = await axios.post<RefreshTokenResponse>(
        `${getApiUrl()}/auth/refresh`,
        { refresh_token: refreshToken },
        {
          headers: {
            'Content-Type': 'application/json'
          }
        }
      );

      const { access_token, refresh_token: newRefreshToken } = response.data;

      // Store the new tokens
      await authStorage.setTokens(access_token, newRefreshToken);

      debugLog('Token refreshed successfully');
      return access_token;
    } catch (error: any) {
      debugLog('Token refresh failed:', error.response?.data || error.message);
      
      // If refresh fails, clear all auth data
      await authStorage.clearAll();
      return null;
    }
  }

  /**
   * Handle token refresh with queue to prevent multiple simultaneous refresh attempts
   */
  async handleTokenRefresh(): Promise<string | null> {
    if (this.isRefreshing) {
      // If already refreshing, wait for the current refresh to complete
      return new Promise((resolve, reject) => {
        this.failedQueue.push({ resolve, reject });
      });
    }

    this.isRefreshing = true;

    try {
      const newToken = await this.refreshToken();
      
      if (newToken) {
        // Process the failed queue with the new token
        this.processQueue(null, newToken);
        return newToken;
      } else {
        // Process the failed queue with error
        this.processQueue(new Error('Token refresh failed'), null);
        return null;
      }
    } catch (error) {
      this.processQueue(error, null);
      return null;
    } finally {
      this.isRefreshing = false;
    }
  }

  /**
   * Process the queue of failed requests
   */
  private processQueue(error: any, token: string | null) {
    this.failedQueue.forEach(({ resolve, reject }) => {
      if (error) {
        reject(error);
      } else {
        resolve(token!);
      }
    });
    
    this.failedQueue = [];
  }

  /**
   * Logout by clearing tokens
   */
  async logout(): Promise<void> {
    try {
      const refreshToken = await authStorage.getRefreshToken();
      if (refreshToken) {
        // Call logout endpoint to revoke refresh token
        await axios.post(
          `${getApiUrl()}/auth/logout`,
          { refresh_token: refreshToken },
          {
            headers: {
              'Content-Type': 'application/json'
            }
          }
        );
      }
    } catch (error) {
      debugLog('Logout API call failed:', error);
      // Continue with local cleanup even if API call fails
    } finally {
      // Clear local storage
      await authStorage.clearAll();
    }
  }
}

export const tokenRefreshService = new TokenRefreshService();
