import axios, { AxiosInstance, AxiosError } from 'axios';
import { getApiUrl, debugLog } from '../utils/env';
import { authStorage } from '../utils/storage';
import { tokenRefreshService } from './tokenRefreshService';
import Constants from 'expo-constants';

// Get API URL from environment utility
const API_URL = getApiUrl();

// Create axios instance with default config
const api: AxiosInstance = axios.create({
  baseURL: API_URL,
  headers: {
    'Content-Type': 'application/json'
  }
});

// Add a request interceptor to include auth token and device ID in requests
api.interceptors.request.use(
  async (config) => {
    const token = await authStorage.getToken();
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }

    // Add device ID for single-device authentication
    const deviceId = Constants.sessionId || Constants.installationId;
    if (deviceId) {
      config.headers['x-device-id'] = deviceId;
    }

    return config;
  },
  (error) => Promise.reject(error)
);

// Add a response interceptor to handle errors and token refresh
api.interceptors.response.use(
  (response) => response,
  async (error: AxiosError) => {
    const originalRequest = error.config as any;

    // Handle 401 errors (token expired)
    if (error.response?.status === 401 && !originalRequest._retry) {
      originalRequest._retry = true;

      try {
        // Attempt to refresh the token
        const newToken = await tokenRefreshService.handleTokenRefresh();

        if (newToken) {
          // Update the authorization header and retry the request
          originalRequest.headers.Authorization = `Bearer ${newToken}`;
          return api(originalRequest);
        } else {
          // Refresh failed, redirect to login or handle accordingly
          debugLog('Token refresh failed, user needs to login again');
          return Promise.reject(error);
        }
      } catch (refreshError) {
        debugLog('Token refresh error:', refreshError);
        return Promise.reject(error);
      }
    }

    // Handle other error cases
    if (error.response) {
      debugLog('Response error:', error.response.data);
    } else if (error.request) {
      debugLog('Request error:', error.request);
    } else {
      debugLog('Error:', error.message);
    }
    return Promise.reject(error);
  }
);

interface AuthResponse {
  access_token: string;
  refresh_token: string;
  [key: string]: any;
}

// Authentication API services
export const authService = {
  // Login with username and password
  login: async (username: string, password: string): Promise<AuthResponse> => {
    try {
      const response = await api.post('/auth/login', { username, password });
      return response.data;
    } catch (error: any) {
      throw error.response?.data?.message || error.message || 'Login failed';
    }
  },

  // Register with username and password
  register: async (username: string, password: string): Promise<AuthResponse> => {
    try {
      const response = await api.post('/auth/register', { username, password });
      return response.data;
    } catch (error: any) {
      throw error.response?.data?.message || error.message || 'Registration failed';
    }
  }
};

// Message API endpoints
export const messageAPI = {
  // Get all messages for the authenticated user
  getAllMessages: async () => {
    const response = await api.get('/api/messages');
    return response.data;
  },

  // Get messages for a specific conversation
  getConversationMessages: async (otherUsername: string) => {
    const response = await api.get(`/api/messages/conversation/${otherUsername}`);
    return response.data;
  },

  // Get pending messages for the authenticated user
  getPendingMessages: async () => {
    const response = await api.get('/api/messages/pending');
    return response.data;
  },

  // Mark messages as delivered
  markAsDelivered: async (messageIds: string[]) => {
    const response = await api.post('/api/messages/delivered', { messageIds });
    return response.data;
  },

  // Mark messages as read
  markAsRead: async (messageIds: string[]) => {
    const response = await api.post('/api/messages/read', { messageIds });
    return response.data;
  },

  // Delete messages
  deleteMessages: async (messageIds: string[]) => {
    const response = await api.delete('/api/messages', { data: { messageIds } });
    return response.data;
  }
};

// Export the API instance for other services
export default api;
