/**
 * Environment variables utility for React Native
 *
 * This file provides type-safe access to environment variables
 * and ensures they are properly loaded based on the current environment.
 *
 * Note: Firebase configuration is NOT included here because @react-native-firebase
 * automatically reads configuration from Google Services files:
 * - Android: android/app/google-services.json
 * - iOS: ios/mobile/GoogleService-Info.plist
 */

// Define the shape of our environment variables
interface EnvVariables {
  API_URL: string;
  WS_URL: string;
  ENV: 'development' | 'staging' | 'production';
  DEBUG: boolean;
}

// Get environment variables with type safety
// Note: React Native doesn't use import.meta.env, we'll use process.env or config
export const env: EnvVariables = {
  API_URL: __DEV__
    ? 'https://tough-disc-merit-median.trycloudflare.com/'
    : 'https://tough-disc-merit-median.trycloudflare.com/',
  WS_URL: __DEV__
    ? 'wss://tough-disc-merit-median.trycloudflare.com/'
    : 'wss://tough-disc-merit-median.trycloudflare.com/',
  ENV: __DEV__ ? 'development' : 'production',
  DEBUG: __DEV__ || false,
};

// Helper functions
export const isDevelopment = (): boolean => env.ENV === 'development';
export const isStaging = (): boolean => env.ENV === 'staging';
export const isProduction = (): boolean => env.ENV === 'production';
export const isDebugMode = (): boolean => env.DEBUG;

// API URL helpers
export const getApiUrl = (): string => env.API_URL;
export const getWsUrl = (): string => env.WS_URL;

// Conditional logging that only works in development/debug mode
export const debugLog = (...args: any[]): void => {
  if (isDebugMode()) {
    console.log('[DEBUG]', ...args);
  }
};

export default env;
