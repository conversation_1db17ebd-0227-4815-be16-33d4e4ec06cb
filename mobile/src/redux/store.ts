import { configureStore } from '@reduxjs/toolkit';
const createSagaMiddleware = require('redux-saga').default
import authReducer from './slices/authSlice';
import chatDBReducer from './slices/chatDBSlice';
import socketReducer from './slices/socketSlice';
import typingReducer from './slices/typingSlice';
import emojiReactionReducer from './slices/emojiReactionSlice';
import fcmReducer from './slices/fcmSlice';
import rootSaga from './sagas/rootSaga';
import Reactotron from '../config/ReactotronConfig';

// Create the saga middleware with Reactotron monitoring
const sagaMiddleware = createSagaMiddleware({
  sagaMonitor: __DEV__ ? Reactotron.createSagaMonitor?.() : undefined,
});

// Configure the store
const store = configureStore({
  reducer: {
    auth: authReducer,
    chatDB: chatDBReducer,
    socket: socketReducer,
    typing: typingReducer,
    emojiReaction: emojiReactionReducer,
    fcm: fcmReducer,
    // Add other reducers here as needed
  },
  middleware: (getDefaultMiddleware) =>
    getDefaultMiddleware({
      thunk: true, // Enable thunk for async actions (FCM uses async thunks)
      serializableCheck: {
        // Ignore these action types if needed
        ignoredActions: [
          'persist/PERSIST',
          'persist/REHYDRATE',
          'persist/PAUSE',
          'persist/PURGE',
          'persist/REGISTER',
        ],
        // Ignore these field paths in all actions
        ignoredActionPaths: ['meta.arg', 'payload.timestamp'],
        // Ignore these paths in the state
        ignoredPaths: ['items.dates'],
      },
    }).concat(sagaMiddleware),
  enhancers: (getDefaultEnhancers) => {
    if (__DEV__ && Reactotron.createEnhancer) {
      return getDefaultEnhancers().concat(Reactotron.createEnhancer());
    }
    return getDefaultEnhancers();
  },
});

// Run the root saga
sagaMiddleware.run(rootSaga);

// Export types
export type RootState = ReturnType<typeof store.getState>;
export type AppDispatch = typeof store.dispatch;

export default store;
