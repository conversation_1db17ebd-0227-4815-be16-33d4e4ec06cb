import { call, put, takeLatest } from 'redux-saga/effects';
import { authService } from '../../services/api';
import {
  loginRequest,
  loginSuccess,
  loginFailure,
  registerRequest,
  registerSuccess,
  registerFailure,
  logout
} from '../slices/authSlice';
import { authStorage } from '../../utils/storage';
import { tokenRefreshService } from '../../services/tokenRefreshService';

interface AuthResponse {
  access_token: string;
  refresh_token: string;
  [key: string]: any;
}

// Worker saga for login
function* loginSaga(action: any) {
  try {
    // Get username and password from action payload
    const { username, password } = action.payload;

    if (!username || !password) {
      throw new Error('Username or password not provided');
    }
console.log('username', username)
console.log('password', password)
    // Call the login API
    const response: AuthResponse = yield call(authService.login, username, password);

    console.log('response', response)

    // Store tokens and username in AsyncStorage for persistence
    yield call(authStorage.setTokens, response.access_token, response.refresh_token);
    yield call(authStorage.setUsername, username);
    yield call(authStorage.setPreviousUsername, username);

    // Dispatch success action with the tokens
    yield put(loginSuccess({
      access_token: response.access_token,
      refresh_token: response.refresh_token,
      username
    }));

  } catch (error: any) {
    // Dispatch failure action with error message
    yield put(loginFailure(error.toString()));
  }
}

// Worker saga for register
function* registerSaga(action: any) {
  try {
    // Get username and password from action payload
    const { username, password } = action.payload;

    if (!username || !password) {
      throw new Error('Username or password not provided');
    }

    // Call the register API
    const response: AuthResponse = yield call(authService.register, username, password);

    // Store tokens and username in AsyncStorage for persistence
    yield call(authStorage.setTokens, response.access_token, response.refresh_token);
    yield call(authStorage.setUsername, username);
    yield call(authStorage.setPreviousUsername, username);

    // Dispatch success action with the tokens
    yield put(registerSuccess({
      access_token: response.access_token,
      refresh_token: response.refresh_token,
      username
    }));

  } catch (error: any) {
    // Dispatch failure action with error message
    yield put(registerFailure(error.toString()));
  }
}

// Worker saga for logout
function* logoutSaga() {
  try {
    // Call the token refresh service to handle logout (revokes refresh token)
    yield call(tokenRefreshService.logout);
  } catch (error: any) {
    console.error('Logout error:', error);
    // Even if logout API call fails, we still clear local storage
    yield call(authStorage.clearAll);
  }
}

// Watcher saga for auth actions
export function* authSaga() {
  yield takeLatest(loginRequest.type, loginSaga);
  yield takeLatest(registerRequest.type, registerSaga);
  yield takeLatest(logout.type, logoutSaga);
}
