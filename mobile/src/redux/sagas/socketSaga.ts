import { eventChannel, EventChannel } from 'redux-saga';
import { call, put, take, takeEvery, cancelled, select } from 'redux-saga/effects';
import { io, Socket } from 'socket.io-client';
import { debugLog } from '../../utils/env';
import {
  connectRequest,
  connectSuccess,
  connectFailure,
  disconnectRequest,
  disconnectSuccess,
  sendMessageRequest,
  sendMessageSuccess,
  sendMessageFailure,
  selectServerUrl,
  selectAuthToken
} from '../slices/socketSlice';
import { selectAuthUser } from '../slices/authSlice';
import { setUserTyping } from '../slices/typingSlice';
import { setUserEmojiReaction } from '../slices/emojiReactionSlice';
import { clearCurrentReceiver, selectCurrentReceiverUsername } from '../slices/chatDBSlice';
import { PayloadAction } from '@reduxjs/toolkit';
import { chatDBService } from '../../database/service';
import { messageSyncService } from '../../services/messageSync';

// Socket instance that will be shared across sagas
let socket: Socket;

// Create a channel for socket events
function createSocketChannel(socket: Socket) {
  return eventChannel(emit => {
    // Handle connect event
    socket.on('connect', () => {
      emit({ type: 'connect', socketId: socket.id });
    });

    // Handle disconnect event
    socket.on('disconnect', () => {
      emit({ type: 'disconnect' });
    });

    // Handle error event
    socket.on('connect_error', (error) => {
      emit({ type: 'error', error });
    });

    // Handle message event - now with message types
    socket.on('message', (data, ack) => {
      // Ensure data has a type field, default to 'text' if not specified
      if (!data.type) {
        data.type = 'text';
      }
      emit({ type: 'message', data });

      // For text messages, use the acknowledgment callback to confirm delivery
      if (data.type === 'text' && data.id && typeof ack === 'function') {
        debugLog('Acknowledging message delivery for:', data.id);

        // Use the receiver_username from the message data if available
        // This is the username of the user receiving the message (current user)
        const receiverUsername = data.receiver_username || 'unknown_user';

        // Send delivery confirmation using the acknowledgment callback
        try {
          const ackData = {
            status: 'delivered',
            message_id: data.id,
            client_message_id: data.client_message_id,
            receiver_username: receiverUsername
          };
          debugLog('Sending acknowledgment data:', ackData);
          ack(ackData);
        } catch (error) {
          debugLog('Error sending acknowledgment:', error);
        }
      }
    });

    // Handle message delivery confirmation
    socket.on('message_delivered', (data) => {
      debugLog('Message delivery confirmation received:', data);
      emit({ type: 'message_delivered', data });
    });

    // Return unsubscribe function
    return () => {
      socket.off('connect');
      socket.off('disconnect');
      socket.off('connect_error');
      socket.off('message');
      socket.off('message_delivered');
    };
  });
}

// Saga to handle socket connection
function* connectSaga(): Generator<any, void, any> {
  try {
    // Get connection details from state
    const serverUrl: string = yield select(selectServerUrl);
    const authToken: string = yield select(selectAuthToken);

    // Create connection options
    const options: any = {};
    if (authToken) {
      options.auth = { token: authToken };
    }

    // Close existing socket if any
    if (socket) {
      socket.disconnect();
    }

    // Connect to the server
    socket = io(serverUrl, options);

    // Create a channel for socket events
    const socketChannel: EventChannel<any> = yield call(createSocketChannel, socket);

    // Process events from the channel
    while (true) {
      const event = yield take(socketChannel);

      // Handle different event types
      switch (event.type) {
        case 'connect':
          yield put(connectSuccess({ socketId: event.socketId }));
          debugLog('✅ Connected as', event.socketId);

          // Sync messages from server using REST API instead of socket-based sync
          try {
            const currentUser: string = yield select(selectAuthUser);
            if (currentUser) {
              debugLog('Starting REST API message sync for user:', currentUser);
              yield call(messageSyncService.syncAllMessages, currentUser);
              debugLog('REST API message sync completed successfully');
            }
          } catch (syncError) {
            debugLog('Failed to sync messages via REST API:', syncError);
          }
          break;
        case 'disconnect':
          debugLog('❌ Disconnected');
          break;
        case 'error':
          yield put(connectFailure(event.error.message || 'Connection error'));
          debugLog('Connection error:', event.error);
          break;
        case 'message_delivered':
          debugLog('📬 Message delivery confirmation:', event.data);

          // Update message status to 'delivered' in the local database
          try {
            // Use client_message_id (local WatermelonDB ID) to update the message
            const localMessageId = event.data.client_message_id;

            if (localMessageId) {
              debugLog('Updating message status to delivered using local ID:', localMessageId);
              yield call(
                chatDBService.updateMessageStatus,
                localMessageId,
                'delivered'
              );
              debugLog('✓✓ Message marked as delivered:', localMessageId);
            } else {
              debugLog('No client_message_id found in delivery confirmation:', event.data);
            }

            // Send delivery confirmation back to server to trigger message deletion
            if (socket && event.data.message_id) {
              debugLog('Sending delivery confirmation to server for message:', event.data.message_id);
              socket.emit('delivery_confirmed', {
                message_id: event.data.message_id
              });
            }
          } catch (error) {
            debugLog('Failed to update message delivery status:', error);
          }
          break;
        case 'message':
          debugLog('📩 Message received:', event.data);

          // Handle received message based on type
          try {
            const currentUser: string = yield select(selectAuthUser);
            // Get sender username from data, fallback to sender_id for backward compatibility
            const senderUsername = event.data.sender_username || event.data.sender_id;

            if (currentUser && senderUsername && event.data.message) {
              // Check message type
              const messageType = event.data.type || 'text';

              switch (messageType) {
                case 'clear_chat':
                  debugLog('Received clear chat request from', senderUsername);
                  // Clear the chat in the local database and update room info
                  yield call(
                    chatDBService.clearRoom,
                    currentUser,
                    senderUsername
                  );

                  // Save the clear_chat message to database and update room info
                  yield call(
                    chatDBService.sendClearChatMessage,
                    senderUsername,
                    currentUser
                  );
                  break;

                case 'delete_user':
                  debugLog('Received delete user request from', senderUsername);
                  // Delete the user room completely
                  yield call(
                    chatDBService.deleteUserRoom,
                    currentUser,
                    senderUsername
                  );

                  // Clear the current receiver to close the chat window
                  yield put(clearCurrentReceiver());
                  break;

                case 'typing':
                  // Handle typing indicator - update typing state in Redux
                  debugLog('Typing indicator from', senderUsername);
                  // Use the dedicated typing slice instead of messageReceived
                  yield put(setUserTyping({
                    userId: senderUsername,
                    isTyping: event.data.message === 'typing'
                  }));
                  break;

                case 'emoji_reaction':
                  console.log('emoji_reaction', event.data.message)
                  // Handle emoji reaction - we don't need to save it to the database
                  debugLog('Emoji reaction from', senderUsername, ':', event.data.message);

                  // Parse the emoji and mood from the message
                  if (event.data.message === 'stopped_reaction') {
                    // Clear the emoji reaction
                    yield put(setUserEmojiReaction({
                      userId: senderUsername,
                      emoji: null,
                      mood: null
                    }));
                  } else {
                    // Set the emoji reaction
                    const parts = event.data.message.split(':');
                    const emoji = parts[0];
                    const mood = parts[1] || 'feeling';

                    yield put(setUserEmojiReaction({
                      userId: senderUsername,
                      emoji,
                      mood
                    }));
                  }
                  break;

                case 'text':
                default:
                  const currentReceiverUsername = yield select(selectCurrentReceiverUsername);
                  // Regular text message - save to database and dispatch to UI

                  console.log('Received message:', event.data.id, 'from:', event.data.sender_username, 'content:', event.data.message.substring(0, 30) + (event.data.message.length > 30 ? '...' : ''));
                  try {
                    yield call(
                      chatDBService.saveMessage,
                      event.data.sender_username,
                      currentUser,
                      event.data.message,
                      false,
                      'text',
                      currentReceiverUsername
                    );
                    console.log('Successfully saved received message to local DB:', event.data.id);
                  } catch (saveError) {
                    console.error('Failed to save received message to local DB:', saveError);
                  }


                  break;
              }
            }
          } catch (dbError) {
            debugLog('Failed to handle received message:', dbError);
          }
          break;
        default:
          break;
      }
    }
  } catch (error: any) {
    debugLog('Socket channel error:', error);
  } finally {
    if (yield cancelled()) {
      // Close the channel if the saga was cancelled
      // socketChannel.close();
    }
  }
}

// Saga to handle sending messages
function* sendMessageSaga(action: PayloadAction<{ receiverUsername: string, messageText: string, messageType?: 'text' | 'clear_chat' | 'typing' | 'delete_user' | 'emoji_reaction' }>): Generator<any, void, any> {
  try {
    const { receiverUsername, messageText, messageType = 'text' } = action.payload;

    if (!socket) {
      throw new Error('Cannot send message: Not connected');
    }

    // Get current user (now this is a username, not a userId)
    const currentUser: string = yield select(selectAuthUser);
    if (!currentUser) {
      throw new Error('User not authenticated');
    }

    console.log('messageType', messageType)

    // Send the message with type field and use acknowledgment callback
    console.log('Sending message with acknowledgment callback');

    // For text messages, we'll save the message first and then update its status when acknowledged
    let savedMessageId: string | null = null;

    if (messageType === 'text') {
      try {
        // Save message to local database with initial 'sending' status
        const savedMessage = yield call(
          chatDBService.saveMessage,
          currentUser,
          receiverUsername,
          messageText,
          true, // Our message
          'text',
          receiverUsername // Current receiver
        );
        savedMessageId = savedMessage.id;
        console.log('Saved message with ID:', savedMessageId);
      } catch (error) {
        console.error('Failed to save message before sending:', error);
      }
    }

    // Prepare message data
    const messageData = {
      receiver_username: receiverUsername, // Using the proper field name
      sender_username: currentUser, // Add sender username explicitly
      message: messageText,
      type: messageType,
      client_message_id: savedMessageId // Include the client's message ID
    };

    // For text messages, use acknowledgment callback
    if (messageType === 'text') {
      socket.emit('message', messageData, function(response: any) {
        // Handle acknowledgment from server
        console.log('Message acknowledgment received:', response);

        // Update message status to 'sent' when acknowledged
        if (savedMessageId && response && response.status === 'acknowledged') {
          // Use a timeout to ensure the database has time to process the message
            chatDBService.updateMessageStatus(savedMessageId!, 'sent')
              .then(() => console.log('Message status updated to sent'))
              .catch(err => console.error('Failed to update message status:', err));
        }
      });
    } else {
      // For non-text messages, don't use acknowledgment callback
      socket.emit('message', messageData);
    }

    // Handle different message types
    switch (messageType) {
      case 'clear_chat':
        debugLog('Sending clear chat request to', receiverUsername);
        try {
          const currentUser: string = yield select(selectAuthUser);
          if (currentUser) {
            // Clear local messages and update room info
            yield call(chatDBService.clearRoom, currentUser, receiverUsername);

            // Send a clear chat message (this will also update room info)
            yield call(
              chatDBService.sendClearChatMessage,
              currentUser,
              receiverUsername
            );
          }
        } catch (dbError) {
          debugLog('Failed to clear chat:', dbError);
        }
        break;

      case 'delete_user':
        debugLog('Sending delete user request to', receiverUsername);
        try {
          const currentUser: string = yield select(selectAuthUser);
          if (currentUser) {
            // Delete the user room completely
            yield call(chatDBService.deleteUserRoom, currentUser, receiverUsername);

            // Clear the current receiver to close the chat window
            yield put(clearCurrentReceiver());
          }
        } catch (dbError) {
          debugLog('Failed to delete user room:', dbError);
        }
        break;

      case 'typing':
        // Don't save typing indicators to database or add to messages list
        debugLog('Sending typing indicator to', receiverUsername);
        // Update our own typing state for consistency
        const currentUser: string = yield select(selectAuthUser);
        if (currentUser) {
          yield put(setUserTyping({
            userId: currentUser,
            isTyping: messageText === 'typing'
          }));
        }
        // No need to save to database or dispatch to messageReceived
        break;

      case 'emoji_reaction':
        // Don't save emoji reactions to database, just send them over the socket
        debugLog('Sending emoji reaction to', receiverUsername);

        // Update our own emoji reaction state for consistency
        const senderUser: string = yield select(selectAuthUser);
        if (senderUser) {
          if (messageText === 'stopped_reaction') {
            // Clear the emoji reaction
            yield put(setUserEmojiReaction({
              userId: senderUser,
              emoji: null,
              mood: null
            }));
          } else {
            // Set the emoji reaction
            const parts = messageText.split(':');
            const emoji = parts[0];
            const mood = parts[1] || 'feeling';

            yield put(setUserEmojiReaction({
              userId: senderUser,
              emoji,
              mood
            }));
          }
        }
        // No need to save to database
        break;

      case 'text':
      default:
        // Message is already saved before sending with 'sending' status
        // The acknowledgment callback will update the status to 'sent'
        // No need to save it again here
        break;
    }

    // Dispatch success action
    yield put(sendMessageSuccess());

  } catch (error: any) {
    // Handle send errors
    yield put(sendMessageFailure(error.message || 'Failed to send message'));
  }
}

// Saga to handle disconnection
function* disconnectSaga(): Generator<any, void, any> {
  try {
    debugLog('Disconnecting socket...');
    if (socket) {
      socket.disconnect();
      debugLog('Socket disconnected successfully');
    } else {
      debugLog('No active socket to disconnect');
    }
    yield put(disconnectSuccess());
  } catch (error: any) {
    debugLog('Disconnect error:', error);
  }
}

// Root socket saga
export function* socketSaga(): Generator<any, void, any> {
  // Handle socket actions
  yield takeEvery(connectRequest.type, connectSaga);
  yield takeEvery(sendMessageRequest.type, sendMessageSaga);
  yield takeEvery(disconnectRequest.type, disconnectSaga);
}
